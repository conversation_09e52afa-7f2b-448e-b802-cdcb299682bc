import subprocess
import json
import os
import sys
import time
import random
import string
from datetime import datetime
import threading
import signal
from concurrent.futures import ProcessPoolExecutor, as_completed
import psutil

# 配置文件
JSONL_FILE = "test_videos.txt" #"/root/novel/ytb_bozhu/all-result.jsonl"
OUTPUT_DIR = "./metadata_downloads"
SUCCESS_LOG = "logs/metadata_success.log"
FAIL_LOG = "logs/metadata_fail.log"

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

# 并发配置
MAX_WORKERS = 10
DELAY_BETWEEN_DOWNLOADS = 1

# 全局变量
log_lock = threading.Lock()
shutdown_event = threading.Event()
executor = None
running_processes = set()

# 统计信息
stats_lock = threading.Lock()
total_processed = 0
total_success = 0
total_failed = 0


def signal_handler(signum, frame):
    """信号处理函数"""
    print(f"\n收到信号 {signum}，正在关闭程序...")
    shutdown_event.set()
    
    global executor
    if executor:
        print("正在关闭进程池...")
        executor.shutdown(wait=False)
    
    cleanup_processes()
    print("程序已关闭")
    sys.exit(0)


def cleanup_processes():
    """清理所有运行中的子进程"""
    current_process = psutil.Process()
    children = current_process.children(recursive=True)
    
    for child in children:
        try:
            print(f"终止子进程 {child.pid}")
            child.terminate()
        except psutil.NoSuchProcess:
            pass
    
    # 等待进程终止
    gone, alive = psutil.wait_procs(children, timeout=3)
    
    # 强制杀死仍然存活的进程
    for p in alive:
        try:
            print(f"强制杀死进程 {p.pid}")
            p.kill()
        except psutil.NoSuchProcess:
            pass


def write_log(log_file, message):
    """线程安全的日志写入"""
    with log_lock:
        with open(log_file, "a", encoding='utf-8') as f:
            f.write(message)


def random_session(length=16):
    """生成随机 session 名称"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def update_stats(success=True):
    """更新统计信息"""
    global total_processed, total_success, total_failed
    with stats_lock:
        total_processed += 1
        if success:
            total_success += 1
        else:
            total_failed += 1


def print_stats():
    """打印统计信息"""
    with stats_lock:
        print(f"\n=== 处理统计 ===")
        print(f"总处理数: {total_processed}")
        print(f"成功: {total_success}")
        print(f"失败: {total_failed}")
        if total_processed > 0:
            success_rate = (total_success / total_processed) * 100
            print(f"成功率: {success_rate:.2f}%")



def download_video_metadata(video_info):
    """下载单个视频的元信息"""
    if shutdown_event.is_set():
        return False

    start_time = time.time()
    session_id = random_session()
    proxy_url = f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

    # 从video_info中提取video_id
    video_id = video_info.get('video_id', '')
    video_type = video_info.get('type', 'unknown')

    if not video_id:
        write_log(FAIL_LOG, f"{datetime.now()} | NO_VIDEO_ID | {video_info}\n")
        return False
    
    if video_type == 'short':
        youtube_url = f"https://www.youtube.com/shorts/{video_id}"
    else:
        youtube_url = f"https://www.youtube.com/watch?v={video_id}"

    # yt-dlp命令：下载最高画质元信息，码率>1500
    cmd = [
        "yt-dlp",
        "--list-formats",  # 列出所有格式
        "--write-info-json",  # 写入元信息JSON
        "--no-download",  # 不下载视频文件
        "--proxy", proxy_url,
        "-o", f"{OUTPUT_DIR}/{video_id}_{video_type}_metadata.%(ext)s",
        youtube_url
    ]

    # 同时获取高质量格式信息
    format_cmd = [
        "yt-dlp",
        "-F",  # 显示所有可用格式
        "--proxy", proxy_url,
        youtube_url
    ]
    
    process = None
    try:
        # 启动子进程
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        running_processes.add(process.pid)
        
        # 等待进程完成
        while process.poll() is None:
            if shutdown_event.is_set():
                print(f"中断下载: {video_id}")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                return False
            time.sleep(0.1)
        
        running_processes.discard(process.pid)
        
        if process.returncode == 0:
            # 获取格式信息
            format_process = subprocess.run(format_cmd, capture_output=True, text=True, timeout=30)
            
            # 解析高质量格式
            high_quality_formats = parse_high_quality_formats(format_process.stdout)
            
            # 保存格式信息到文件
            format_info = {
                'video_id': video_id,
                'video_type': video_type,
                'youtube_url': youtube_url,
                'original_info': video_info,
                'high_quality_formats': high_quality_formats,
                'download_time': datetime.now().isoformat(),
                'processing_time': time.time() - start_time
            }

            format_file = f"{OUTPUT_DIR}/{video_id}_{video_type}_formats.json"
            with open(format_file, 'w', encoding='utf-8') as f:
                json.dump(format_info, f, ensure_ascii=False, indent=2)
            
            update_stats(True)
            write_log(SUCCESS_LOG, f"{datetime.now()} | {video_id} | session={session_id} | SUCCESS | time={time.time()-start_time:.2f}s\n")
            print(f"成功处理: {video_id} - {time.time()-start_time:.2f}s")
            return True
        else:
            stderr_output = process.stderr.read() if process.stderr else "Unknown error"
            update_stats(False)
            write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={session_id} | FAIL | error={stderr_output}\n")
            print(f"处理失败: {video_id}")
            return False
            
    except Exception as e:
        if process:
            running_processes.discard(process.pid)
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        update_stats(False)
        write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={session_id} | EXCEPTION: {e}\n")
        print(f"处理异常: {video_id} - {e}")
        return False


def parse_high_quality_formats(format_output):
    """解析高质量格式信息（码率>1500）"""
    high_quality_formats = []
    lines = format_output.split('\n')
    
    for line in lines:
        if 'mp4' in line or 'webm' in line:
            # 简单解析格式行，寻找码率信息
            parts = line.split()
            for part in parts:
                if 'k' in part and part.replace('k', '').replace('.', '').isdigit():
                    try:
                        bitrate = float(part.replace('k', ''))
                        if bitrate > 1500:  # 码率大于1500k
                            high_quality_formats.append({
                                'format_line': line.strip(),
                                'estimated_bitrate': bitrate
                            })
                            break
                    except ValueError:
                        continue
    
    return high_quality_formats


def download_with_delay(video_info):
    """带延迟的下载函数"""
    time.sleep(random.uniform(0, DELAY_BETWEEN_DOWNLOADS))
    return download_video_metadata(video_info)


def read_jsonl_file(file_path):
    """读取包含video_id和short_id的JSON文件"""
    video_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)

                        # 处理video_id数组
                        if 'video_id' in data and isinstance(data['video_id'], list):
                            for vid in data['video_id']:
                                video_info = {
                                    'video_id': vid,
                                    'type': 'video',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                        # 处理short_id数组
                        if 'short_id' in data and isinstance(data['short_id'], list):
                            for sid in data['short_id']:
                                video_info = {
                                    'video_id': sid,
                                    'type': 'short',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                    except json.JSONDecodeError as e:
                        print(f"解析第{line_num}行JSON失败: {e}")
                        continue
        return video_list
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 读取JSONL文件
    print(f"正在读取文件: {JSONL_FILE}")
    video_list = read_jsonl_file(JSONL_FILE)
    
    if not video_list:
        print("没有找到有效的视频信息")
        return
    
    print(f"找到 {len(video_list)} 个视频，开始处理...")
    
    global executor
    try:
        executor = ProcessPoolExecutor(max_workers=MAX_WORKERS)
        
        # 提交所有任务
        future_to_video = {executor.submit(download_with_delay, video_info): video_info for video_info in video_list}
        
        # 处理完成的任务
        for future in as_completed(future_to_video):
            if shutdown_event.is_set():
                print("收到关闭信号，停止处理...")
                break
            
            video_info = future_to_video[future]
            try:
                result = future.result(timeout=1)
                # 处理结果（如果需要的话）
            except Exception as exc:
                video_id = video_info.get('video_id', 'unknown')
                video_type = video_info.get('type', 'unknown')
                print(f"处理异常: {video_id} ({video_type}) - {exc}")
                write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | {video_type} | EXCEPTION: {exc}\n")
    
    except KeyboardInterrupt:
        print("\n收到键盘中断信号...")
        shutdown_event.set()
    finally:
        if executor:
            print("正在关闭进程池...")
            executor.shutdown(wait=False)
            executor = None
    
    print_stats()
    print("处理完成")


if __name__ == "__main__":
    main()
