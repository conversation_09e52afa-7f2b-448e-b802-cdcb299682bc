import os
import random
import string

# Data Directory
DATA_DIR = "data"

# Concurrency Settings
MAX_WORKERS = 5

# Upload to S3
UPLOAD_TO_S3 = True  # Set to False to disable upload
DELETE_LOCAL_VIDEO = True  # Set to False to keep local video files after upload

# S3 Credentials
S3_ENDPOINT_URL = os.getenv("S3_ENDPOINT_URL", "https://6d18700b4bd3fff2b330035c35b0bbeb.r2.cloudflarestorage.com")
S3_BUCKET = os.getenv("S3_BUCKET", "tmp-transfer2")
S3_REGION = os.getenv("S3_REGION", "auto")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "6e17dce5e6699ec405f7ec07deecf321")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "4916026eb627351979f06b898b4f8f67137caba46c39bab83934eb29a97f92ad")
S3_PREFIX = "video/0829/"

# Boto3 Client Config
BOTO_CONNECT_TIMEOUT = 10  # seconds
BOTO_READ_TIMEOUT = 30  # seconds

# Proxy Settings
USE_PROXY = True  # Set to False to disable proxy

# List of available proxies. Add more dictionaries to this list.
PROXIES = [
    {
        "name": "JingTong",
        "host": "***************",
        "port": "6260",
        "user": "rqbRB1gn",
        "pass": "LU2k2Mrg",
        "weight": 5
    },
    {
        "name": "Novabpm",
        "host": "novabpm-ins-7ws6jjkn.novada.pro",
        "port": "7788",
        "user": "5851b070f69-zone-adam",
        "pass": "t5gNbn29fwu2Rlhc",
        "weight": 100
    }
    # Add more proxies here in the future, for example:
    # {
    #     "host": "proxy.example.com",
    #     "port": "8888",
    #     "user": "another_user",
    #     "pass": "another_pass"
    # }
]

def generate_proxy_url():
    """
    Selects a random proxy from the list based on weight and generates a proxy URL.
    Returns a tuple containing the proxy URL and the selected proxy config.
    """
    if not USE_PROXY or not PROXIES:
        return None, None

    # Separate proxies and weights
    proxies = PROXIES
    weights = [proxy.get('weight', 1) for proxy in proxies]

    # Select a proxy based on weight
    # random.choices returns a list, so we take the first element
    proxy_config = random.choices(proxies, weights=weights, k=1)[0]

    proxy_url = (
        f"http://{proxy_config['user']}:{proxy_config['pass']}"
        f"@{proxy_config['host']}:{proxy_config['port']}"
    )
    return proxy_url, proxy_config


# real
# LARK_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/3fd52520-7ce9-46f4-9491-b12dbc61c30d"

# self test
LARK_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/9d95585d-8d8e-478e-967e-72c31bdf833d"
