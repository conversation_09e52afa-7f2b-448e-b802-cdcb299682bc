import random
import string

# Data Directory
DATA_DIR = "data"

# Concurrency Settings
MAX_WORKERS = 5

# Upload to Volcengine
UPLOAD_TO_VOLC = True  # Set to False to disable upload

# Volcengine Credentials
VOLC_AK = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
VOLC_SK = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='
VOLC_ENDPOINT = 'tos-cn-shanghai.volces.com'
VOLC_REGION = "cn-shanghai"
VOLC_BUCKET = 'yunzhi-video'
VOLC_FOLDER = "youtube_videos"  # Specify the folder in the bucket

# Proxy Settings
USE_PROXY = True # Set to False to disable proxy
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = "7788"
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

def generate_proxy_url():
    """
    Generates a proxy URL with a random session ID for each run.
    """
    session_id = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    username = f"{PROXY_USER}-session-{session_id}"
    return f"http://{username}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

PROXY = generate_proxy_url() if USE_PROXY else None

# real
# LARK_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/3fd52520-7ce9-46f4-9491-b12dbc61c30d"

# self test
LARK_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/9d95585d-8d8e-478e-967e-72c31bdf833d"
