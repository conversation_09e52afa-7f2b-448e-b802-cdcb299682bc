import os
import yt_dlp
from config import generate_proxy_url

def download_video(video_url, file_lock, data_dir):
    """
    Downloads a video from a given URL.
    """
    PROXY_URL, PROXY_INFO = generate_proxy_url()
    if PROXY_URL and PROXY_INFO:
        proxy_name = PROXY_INFO.get('name', f"{PROXY_INFO['user']}@{PROXY_INFO['host']}")
        print(f"Using proxy: {proxy_name}")
    
    ydl_opts = {
        # 'format': 'bestvideo[height>=1080][vcodec!=av01]+bestaudio/best',
        'format': '133',
        'writeinfojson': True,
        'addmetadata': True,
        'merge_output_format': 'mp4',
        'proxy': PROXY_URL,
        'outtmpl': os.path.join(data_dir, 'downloads', '%(id)s_audio_video.%(ext)s'),
        'http_chunk_size': 30 * 1024 * 1024,  # 30MB
        'retries': 11,
        'file_access_retries': 12,
        'fragment_retries': 13,
        'keepfragments': False,
        'socket_timeout': 360,  # 360 seconds
        "concurrent-fragments": 10
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(video_url, download=True)
            file_path = ydl.prepare_filename(info_dict)
            print(f"Downloaded '{info_dict['title']}' successfully.")
            
            with file_lock:
                with open(os.path.join(data_dir, "downloaded_video.txt"), "a") as f:
                    f.write(f"{file_path}\n")
            return file_path
    except Exception as e:
        print(f"Error downloading {video_url}: {e}")
        return None