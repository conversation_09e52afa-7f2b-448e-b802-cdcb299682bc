import os
import yt_dlp
from config import PROXY

def download_video(video_url, file_lock, data_dir):
    """
    Downloads a video from a given URL.
    """
    ydl_opts = {
        'format': 'bestvideo[height>=1080][vcodec!=av01]+bestaudio/best',
        'writeinfojson': True,
        'addmetadata': True,
        'merge_output_format': 'mp4',
        'proxy': PROXY,
        'outtmpl': os.path.join(data_dir, 'downloads', '%(id)s_audio_video.%(ext)s'),
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(video_url, download=True)
            file_path = ydl.prepare_filename(info_dict)
            print(f"Downloaded '{info_dict['title']}' successfully.")
            
            with file_lock:
                with open(os.path.join(data_dir, "downloaded_video.txt"), "a") as f:
                    f.write(f"{file_path}\n")
            return file_path
    except Exception as e:
        print(f"Error downloading {video_url}: {e}")
        return None