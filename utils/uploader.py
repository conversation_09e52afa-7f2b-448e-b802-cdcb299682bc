import os
import boto3
from botocore.client import Config
from botocore.exceptions import Client<PERSON>rror
from config import (
    UPLOAD_TO_S3,
    S3_ENDPOINT_URL,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    S3_BUCKET,
    S3_PREFIX,
    BOTO_CONNECT_TIMEOUT,
    BOTO_READ_TIMEOUT,
    DELETE_LOCAL_VIDEO
)

def upload_to_s3(file_path, file_lock, data_dir):
    """
    Uploads a file to an S3-compatible service like Cloudflare R2
    and deletes it locally after a successful upload.

    Args:
        file_path (str): The path of the file to upload.
        file_lock (threading.Lock): Lock for thread-safe file operations.
        data_dir (str): The base data directory.
    """
    if not UPLOAD_TO_S3:
        print("S3 upload is disabled. Skipping.")
        return

    if not all([S3_ENDPOINT_URL, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, S3_BUCKET]):
        print("S3 credentials are not fully configured. Skipping upload.")
        return

    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            config=Config(
                connect_timeout=BOTO_CONNECT_TIMEOUT,
                read_timeout=BOTO_READ_TIMEOUT,
                signature_version='s3v4'
            )
        )
        
        object_key = f"{S3_PREFIX}{os.path.basename(file_path)}"
        
        print(f"Uploading {file_path} to s3://{S3_BUCKET}/{object_key}")
        s3_client.upload_file(file_path, S3_BUCKET, object_key)
        
        with file_lock:
            processed_log_path = os.path.join(data_dir, "processed_video.txt")
            with open(processed_log_path, "a", encoding='utf-8') as f:
                f.write(object_key + "\n")
        print(f"Uploaded and logged: {object_key}")

        if DELETE_LOCAL_VIDEO:
            os.remove(file_path)
            print(f"Deleted local file: {file_path}")
        else:
            print(f"Kept local file as per config: {file_path}")

    except ClientError as e:
        print(f"Failed to upload {file_path} to S3. Boto3 ClientError: {e}")
    except Exception as e:
        print(f"Failed to upload {file_path}. An unexpected error occurred: {e}")