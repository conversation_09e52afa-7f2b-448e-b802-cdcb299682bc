import os
from config import VOLC_AK, VOLC_SK, VOLC_ENDPOINT, VOLC_REGION, VOLC_BUCKET, VOLC_FOLDER
import tos

def upload_to_volc(file_path, file_lock, data_dir):
    """
    Uploads a file to Volcengine TOS and deletes it locally after a successful upload.

    Args:
        file_path (str): The path of the file to upload.
    """
    if not all([VOLC_AK, VOLC_SK, VOLC_ENDPOINT, VOLC_REGION, VOLC_BUCKET]):
        print("Volcengine credentials are not fully configured. Skipping upload.")
        return

    try:
        client = tos.TosClientV2(VOLC_AK, VOLC_SK, VOLC_ENDPOINT, VOLC_REGION)
        object_key = f"{VOLC_FOLDER}/{os.path.basename(file_path)}"
        client.put_object_from_file(VOLC_BUCKET, object_key, file_path)
        
        with file_lock:
            with open(os.path.join(data_dir, "processed_video.txt"), "a") as f:
                f.write(object_key + "\n")
        print("Uploaded and logged: {}".format(object_key))
        
        os.remove(file_path)
        print("Deleted local file: {}".format(file_path))

    except Exception as e:
        print("Failed to upload {}. Error: {}".format(file_path, e))