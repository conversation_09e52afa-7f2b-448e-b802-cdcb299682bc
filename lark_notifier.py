import os
import tos
import requests
import json
from datetime import datetime, timedelta, timezone
from config import VOLC_AK, VOLC_SK, VOLC_ENDPOINT, VOLC_REGION, VOLC_BUCKET, LARK_WEBHOOK_URL, VOLC_FOLDER

def get_tos_file_stats():
    """
    Fetches file statistics from Volcengine TOS.
    """
    if not all([VOLC_AK, VOLC_SK, VOLC_ENDPOINT, VOLC_REGION, VOLC_BUCKET]):
        print("Volcengine credentials are not fully configured.")
        return None

    try:
        client = tos.TosClientV2(
            ak=VOLC_AK,
            sk=VOLC_SK,
            endpoint=VOLC_ENDPOINT,
            region=VOLC_REGION,
        )
        
        total_files = 0
        total_size = 0
        latest_upload_time = None
        
        last_hour_files = 0
        last_hour_size = 0
        
        now = datetime.now(timezone.utc)
        one_hour_ago = now - timedelta(hours=1)

        # Paginate through all objects in the bucket
        marker = None
        while True:
            out = client.list_objects(bucket=VOLC_BUCKET, marker=marker, prefix=VOLC_FOLDER)
            for obj in out.contents:
                total_files += 1
                total_size += obj.size
                
                last_modified = obj.last_modified.astimezone(timezone.utc)

                if latest_upload_time is None or last_modified > latest_upload_time:
                    latest_upload_time = last_modified

                if last_modified > one_hour_ago:
                    last_hour_files += 1
                    last_hour_size += obj.size
            
            if out.is_truncated:
                marker = out.next_marker
            else:
                break
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "latest_upload_time": latest_upload_time,
            "last_hour_files": last_hour_files,
            "last_hour_size": last_hour_size,
        }

    except Exception as e:
        print(f"Error fetching stats from Volcengine: {e}")
        return None

def format_size(size_bytes):
    """
    Formats size in bytes to a human-readable format (KB, MB, GB).
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.2f} KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes/1024**2:.2f} MB"
    else:
        return f"{size_bytes/1024**3:.2f} GB"

def send_lark_notification(stats):
    """
    Sends a formatted notification to Lark.
    """
    if not LARK_WEBHOOK_URL or LARK_WEBHOOK_URL == "your_lark_webhook_url":
        print("Lark webhook URL is not configured. Skipping notification.")
        return

    if not stats:
        print("No stats to send.")
        return

    latest_time_str = stats['latest_upload_time'].astimezone(timezone(timedelta(hours=8))).strftime('%Y-%m-%d %H:%M:%S')

    card_content = {
        "config": {
            "wide_screen_mode": True
        },
        "header": {
            "template": "blue",
            "title": {
                "content": "火山引擎ytb存储桶报告",
                "tag": "plain_text"
            }
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": f"**总量统计**\n<font color='grey'>最近上传: {latest_time_str}</font>",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            },
            {
                "tag": "div",
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "content": f"**文件总数**\n{stats['total_files']}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": f"**总大小**\n{format_size(stats['total_size'])}",
                            "tag": "lark_md"
                        }
                    }
                ]
            },
            {
                "tag": "div",
                "text": {
                    "content": "**最近一小时报告**",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            },
            {
                "tag": "div",
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "content": f"<font color='green'>**新增文件数**\n{stats['last_hour_files']}</font>",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": f"<font color='green'>**新增大小**\n{format_size(stats['last_hour_size'])}</font>",
                            "tag": "lark_md"
                        }
                    }
                ]
            }
        ]
    }

    payload = {
        "msg_type": "interactive",
        "card": card_content
    }

    try:
        response = requests.post(LARK_WEBHOOK_URL, headers={'Content-Type': 'application/json'}, data=json.dumps(payload))
        response.raise_for_status()
        print("Lark notification sent successfully.")
    except requests.exceptions.RequestException as e:
        print(f"Error sending Lark notification: {e}")

if __name__ == "__main__":
    stats = get_tos_file_stats()
    if stats:
        send_lark_notification(stats)