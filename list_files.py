
import tos

bucket_name = 'yunzhi-video'
endpoint_url = 'https://tos-cn-shanghai.volces.com'  # TOS原生endpoint
access_key = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
secret_key = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='

# TOS客户端
tos_client = None


def init_tos_client():
    """初始化TOS客户端"""
    global tos_client
    try:
        # 使用原生TOS SDK，直接使用原始secret key
        tos_client = tos.TosClientV2(
            region='cn-shanghai',
            ak=access_key,
            sk=secret_key,
            endpoint=endpoint_url
        )

        # 测试连接
        tos_client.head_bucket(bucket=bucket_name)
        print("TOS客户端初始化成功")
        return True
    except Exception as e:
        print(f"TOS客户端初始化失败: {e}")
        return False


init_tos_client()

resp = tos_client.list_objects(bucket=bucket_name, prefix="youtube_videos")
for obj in resp.contents:
    print(obj.key)