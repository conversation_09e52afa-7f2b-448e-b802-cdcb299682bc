import concurrent.futures
import argparse
import threading
import os
from utils.downloader import download_video
from utils.uploader import upload_to_volc
from config import UPLOAD_TO_VOLC, MAX_WORKERS, DATA_DIR

# Create a lock to prevent race conditions when writing to files
file_lock = threading.Lock()

def resume_interrupted_uploads(data_dir):
    """
    Finds downloaded but not uploaded files and resumes their upload.
    """
    print("Checking for interrupted uploads...")
    processed_log_path = os.path.join(data_dir, "processed_video.txt")
    downloads_dir = os.path.join(data_dir, "downloads")

    uploaded_files = set()
    if os.path.exists(processed_log_path):
        with open(processed_log_path, "r") as f:
            uploaded_files = {line.strip() for line in f}

    if not os.path.exists(downloads_dir):
        return

    for filename in os.listdir(downloads_dir):
        # Process only video files, not .json files etc.
        if filename.endswith(('.mp4', '.mkv', '.webm')):
            if filename not in uploaded_files:
                print(f"Found interrupted upload: {filename}. Resuming upload...")
                file_path = os.path.join(downloads_dir, filename)
                if UPLOAD_TO_VOLC:
                    upload_to_volc(file_path, file_lock, data_dir)

def process_video(video_url, data_dir):
    """
    Downloads a video and then uploads it.
    """
    downloaded_file_path = download_video(video_url, file_lock, data_dir)
    if downloaded_file_path and UPLOAD_TO_VOLC:
        upload_to_volc(downloaded_file_path, file_lock, data_dir)

def get_processed_videos(file_path):
    if not os.path.exists(file_path):
        return set()
    with open(file_path, 'r') as f:
        return {line.strip().split('_')[0] for line in f if line.strip()}

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download and upload videos from a list of URLs.")
    parser.add_argument('-f', '--file', default='video_urls.txt', help='Path to the file containing video URLs.')
    parser.add_argument('-d', '--data-dir', default=DATA_DIR, help='Directory to store downloaded videos and logs.')
    args = parser.parse_args()

    data_dir = args.data_dir
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    downloads_path = os.path.join(data_dir, 'downloads')
    if not os.path.exists(downloads_path):
        os.makedirs(downloads_path)

    # Resume any uploads that were interrupted before processing new videos
    if UPLOAD_TO_VOLC:
        resume_interrupted_uploads(args.data_dir)

    processed_downloaded = get_processed_videos(os.path.join(data_dir, 'downloaded_video.txt'))
    processed_uploaded = get_processed_videos(os.path.join(data_dir, 'processed_video.txt'))
    processed_ids = processed_downloaded.union(processed_uploaded)

    try:
        with open(args.file, 'r') as f:
            video_urls = []
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                video_id = line
                if '=' in line:
                    video_id = line.split('=')[-1]

                if video_id in processed_ids:
                    print(f"Skipping already processed video: {video_id}")
                    continue

                if line.startswith('http'):
                    video_urls.append(line)
                else:
                    video_urls.append(f"https://www.youtube.com/watch?v={line}")
    except FileNotFoundError:
        print(f"Error: The file '{args.file}' was not found.")
        exit(1)

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_url = {executor.submit(process_video, url, data_dir): url for url in video_urls}
        for future in concurrent.futures.as_completed(future_to_url):
            url = future_to_url[future]
            try:
                future.result()
            except Exception as exc:
                print('%r generated an exception: %s' % (url, exc))